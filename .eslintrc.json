{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"indent": ["error", 2], "linebreak-style": ["error", "unix"], "quotes": ["error", "double"], "semi": ["error", "always"], "no-unused-vars": "warn", "no-console": "off", "prefer-const": "error", "prefer-template": "error", "template-curly-spacing": ["error", "never"], "space-infix-ops": "error", "keyword-spacing": "error", "space-before-blocks": "error", "object-curly-spacing": ["error", "always"], "array-bracket-spacing": ["error", "never"], "comma-spacing": ["error", {"before": false, "after": true}], "no-trailing-spaces": "error", "eol-last": "error"}}